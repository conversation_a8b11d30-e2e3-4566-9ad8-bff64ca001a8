import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, Index } from "typeorm";
import { PrimaryBaseEntity } from "../primary-base.entity";
import { NSPayment } from "~/common/enums/payment.enum";

/**
 * H<PERSON><PERSON> hạn, không dùng xài hết thì bắt buộc mua lại gói khác
 */
@Entity('member_package')
export class MemberPackageEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'ID của thành viên' })
    @Column({ type: 'uuid' })
    @Index()
    memberId: string;

    @ApiProperty({ description: 'ID của đơn hàng' })
    @Column({ type: 'uuid' })
    @Index()
    orderId: string;

    @ApiProperty({ description: 'ID của gói dịch vụ' })
    @Column({ type: 'uuid' })
    @Index()
    packagePlanId: string;

    @ApiProperty({ description: '<PERSON><PERSON><PERSON> hết hạn' })
    @Column({ type: 'timestamptz', nullable: true })
    expiredDate: Date;

    @ApiProperty({ description: '<PERSON><PERSON>y kích hoạt' })
    @Column({ type: 'timestamptz', nullable: true })
    activatedDate: Date;

    // Ngày thanh toán tiếp theo
    @ApiProperty({ description: 'Ngày thanh toán tiếp theo' })
    @Column({ type: 'timestamptz', nullable: true })
    nextPaymentDate: Date;

    @ApiProperty({ description: 'Số lượng giao dịch được phép' })
    @Column({ type: 'int' })
    initialTransactionLimit: number;

    @ApiProperty({ description: 'Số lượng giao dịch hiện tại' })
    @Column({ type: 'int' })
    currentTransaction: number;

    @ApiProperty({ description: 'Số lượng cấu hình được phép' })
    @Column({ type: 'int' })
    initialConfigLimit: number;

    @ApiProperty({ description: 'Số lượng cấu hình hiện tại' })
    @Column({ type: 'int' })
    currentConfig: number;

    @ApiProperty({ description: 'Trạng thái' })
    @Column({ type: 'varchar', length: 20, default: 'PENDING' })
    status: string;

    @ApiProperty({ description: 'Là gói dịch vụ tự động gia hạn' })
    @Column({ type: 'boolean', default: false })
    isAutoRenewal: boolean;

    @ApiProperty({ description: 'ID của subscription' })
    @Column({ type: 'varchar', nullable: true })
    subscriptionId: string;

    @ApiProperty({ description: 'Đối tác cung cấp subscription' })
    @Column({ type: 'varchar', nullable: true })
    subscriptionProvider: NSPayment.EPaymentProvider;
}