import { Entity, Column, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('member_key')
export class MemberKeyEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'ID của thành viên' })
  @Column({ type: 'uuid' })
  @Index()
  memberId: string;

  @ApiProperty({ description: 'Public key' })
  @Column({ type: 'varchar', length: 255 })
  publicKey: string;

  @ApiProperty({ description: 'API key' })
  @Column({ type: 'varchar', length: 255 })
  apiKey: string;
}
