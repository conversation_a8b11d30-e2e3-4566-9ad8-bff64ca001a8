import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';

export class DashboardLogDto {
    @ApiProperty({ description: 'ID cấu hình' })
    @IsNotEmpty()
    configId: string;

    @ApiProperty({ description: 'Là request test hay không' })
    @IsBoolean()
    @Transform(({value}) => value === 'true' || value === true)
    isTest: boolean = true;
}
