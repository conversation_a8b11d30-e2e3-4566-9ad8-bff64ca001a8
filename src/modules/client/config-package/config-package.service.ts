import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import {
  ConfigLogDto,
  ConfigPackageListDto,
  CreateConfigPackageDto,
  UpdateConfigPackageDto,
} from './dto/config-package.dto';
import {
  ConfigPackageRepo,
  ConfigPackageDetailRepo,
} from '~/domains/primary/config-package/config-package.repo';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { NSConfig } from '~/common/enums/config.enum';
import { ConfigPackageDetailEntity } from '~/domains/primary/config-package/config-package-detail.entity';
import * as dayjs from 'dayjs';
import { MemberApiRepo } from '~/domains/primary/member-api/member-api.repo';
import * as _ from 'lodash';
import { generateCurlText, generatePayloadFields } from '~/common/helpers/generate-text.helper';
import { configEnv } from '~/@config/env';
import { MemberApiLogRepo } from '~/domains/primary/member-api-log/member-api-log.repo';
import { MemberPackageRepo } from '~/domains/primary/member-package/member-package.repo';
import { generateCodeHelper } from '~/common/helpers/generate-code.helper';
import { memberSessionContext } from '../member-session.context';
import { BusinessException } from '~/@systems/exceptions';
import { Raw } from 'typeorm';
import { MemberKeyRepo } from '~/domains/primary';
import { MemberRepo } from '~/domains/primary';
import { NSMember } from '~/common/enums';
import { DashboardLogDto } from './dto/dashboard.dto';
import { dateHelper } from '~/common/helpers/date.helper';

@Injectable()
export class ConfigPackageService {
  constructor() { }

  @BindRepo(ConfigPackageRepo)
  private configRepo: ConfigPackageRepo;

  @BindRepo(ConfigPackageDetailRepo)
  private detailRepo: ConfigPackageDetailRepo;

  @BindRepo(MemberApiRepo)
  private memberApiRepo: MemberApiRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @BindRepo(MemberKeyRepo)
  private memberKeyRepo: MemberKeyRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @DefTransaction()
  async create(dto: CreateConfigPackageDto) {
    const { memberId } = memberSessionContext;
    const packagePlanMember = await this.memberPackageRepo.findOne({
      where: {
        id: dto.packagePlanId,
      },
    });
    if (!packagePlanMember) throw new BusinessException('Member package not found');
    if (packagePlanMember.currentConfig >= packagePlanMember.initialConfigLimit) {
      throw new BusinessException('You have reached the maximum number of configurations');
    }

    const member = await this.memberRepo.findOne({ where: { id: memberId } });
    if (!member) throw new BusinessException('Member not found');
    if (member.statusValidate !== NSMember.EStatus.VALIDATED) {
      throw new BusinessException('Member not validated');
    }
    const memberKey = await this.memberKeyRepo.findOne({ where: { memberId } });
    if (!memberKey) throw new BusinessException('Member key not found');

    // Update Member Package
    await this.memberPackageRepo.update(
      {
        id: packagePlanMember.id,
      },
      {
        currentConfig: packagePlanMember.currentConfig + 1,
      },
    );

    // Tạo config package
    const config = this.configRepo.create({ ...dto, memberId });
    if (!config.code) config.code = generateCodeHelper.generateCode('CP', 8);
    await this.configRepo.save(config);
    const details = dto.fields?.map(detail =>
      this.detailRepo.create({
        ...detail,
        configPackageId: config.id,
      }),
    );
    if (details?.length) await this.detailRepo.save(details);

    // Gen API
    const apiGen = await this.generateMemberAPIConfig(config.id);

    return { ...config, fields: details || [], apiInfo: apiGen };
  }

  async findAll() {
    const { memberId } = memberSessionContext;
    return this.configRepo.find({
      where: { memberId, status: NSConfig.EStatus.ACTIVE },
      order: { createdDate: 'DESC' },
    });
  }

  async findPagination(body: ConfigPackageListDto) {
    const { memberId } = memberSessionContext;
    const { code, name, status, fromDate, toDate, ...pageRequest } = body;
    const where: any = { memberId };
    if (code) {
      where.code = code;
    }
    if (name) {
      where.name = name;
    }
    if (status) {
      where.status = status;
    }
    if (fromDate && toDate) {
      if (fromDate && toDate) {
        where.createdDate = Raw(alias =>
          `DATE(${alias}) BETWEEN DATE(:from) AND DATE(:to)`,
          { from: fromDate, to: toDate }
        );
      }
    }
    return this.configRepo.findPagination({ where, order: { createdDate: 'DESC' } }, pageRequest);
  }

  async findOne(id: string) {
    const config = await this.configRepo.findOne(id);
    if (!config) throw new NotFoundException('Config package not found');
    const details = await this.detailRepo.find({ where: { configPackageId: config.id } });

    const apiInfo = await this.memberApiRepo.findOne({ where: { configId: config.id } });
    return {
      ...config,
      apiInfo,
      fields: details || [],
    };
  }

  async findLogs(body: ConfigLogDto) {
    const { configId, ...pageRequest } = body;
    return this.memberApiLogRepo.findPagination(
      { where: { configId: body.configId }, order: { createdDate: 'DESC' } },
      pageRequest,
    );
  }

  @DefTransaction()
  async update(dto: UpdateConfigPackageDto) {
    const { id, ...updateDto } = dto;
    const config = await this.findOne(id);
    await this.configRepo.update(
      {
        id,
      },
      {
        name: updateDto.name ?? config.name,
        description: updateDto.description ?? config.description,
      },
    );

    // Xoá toàn bộ detail cũ nếu có cập nhật
    if (dto.details) {
      await this.detailRepo.delete({ configPackageId: id });
      const newDetails = dto.details.map(detail =>
        this.detailRepo.create({ ...detail, configPackageId: id }),
      );
      await this.detailRepo.save(newDetails);
    }
    // Gen lại API
    const apiInfo = await this.generateMemberAPIConfig(id);
    return { ...config, apiInfo };
  }

  @DefTransaction()
  async inActive(id: string) {
    const config = await this.findOne(id);
    if (!config) throw new NotFoundException('Config package not found');
    return await this.configRepo.update({ id }, { status: NSConfig.EStatus.INACTIVE });
  }

  @DefTransaction()
  async active(id: string) {
    const config = await this.findOne(id);
    if (!config) throw new NotFoundException('Config package not found');
    return await this.configRepo.update({ id }, { status: NSConfig.EStatus.ACTIVE });
  }

  //#region Generate API
  @DefTransaction()
  async generateMemberAPIConfig(configId: string) {
    const config = await this.configRepo.findOne(configId);
    if (!config) throw new NotFoundException('Config package not found');

    const details = await this.detailRepo.find({ where: { configPackageId: config.id } });
    if (!details || !details.length) {
      throw new BadRequestException('Cấu hình không có field nào');
    }

    const apiKey = await this.memberKeyRepo.findOne({ where: { memberId: config.memberId } });
    if (!apiKey) throw new NotFoundException('Member key not found');

    const { EXTERNAL_API_HOST, EXTERNAL_API_PATH } = configEnv();

    const path = `${EXTERNAL_API_PATH}/${config.id}`;
    const host = EXTERNAL_API_HOST; // Có thể lấy từ ENV hoặc config
    const fullUrl = `${host}${path}`;
    const method = 'POST';

    const samplePayload = generatePayloadFields(details);
    const curlText = generateCurlText(fullUrl, samplePayload, method, apiKey.apiKey);

    await this.memberApiRepo.delete({ configId });

    const memberApi = this.memberApiRepo.create({
      configId: config.id,
      memberId: config.memberId,
      host,
      path,
      method,
      body: samplePayload,
      curlText,
    });

    const data = await this.memberApiRepo.save(memberApi);
    return data;
  }
  //#endregion

  //#region Handle incoming external API
  @DefTransaction()
  async handleIncomingExternalApi(body: any, configId: string) {
    const config = await this.configRepo.findOne(configId);
    if (!config) throw new NotFoundException('Config package not found');

    const details = await this.detailRepo.find({ where: { configPackageId: config.id } });
    if (!details || !details.length) {
      throw new BadRequestException('Missing config details');
    }

    const memberAPI = await this.memberApiRepo.findOne({ where: { configId } });
    if (!memberAPI) throw new NotFoundException('Member API not found');

    const packagePlanMember = await this.memberPackageRepo.findOne({
      where: { id: config.packagePlanId },
    });
    if (!packagePlanMember) throw new NotFoundException('Member package not found');
    if (packagePlanMember.status !== 'ACTIVE') {
      throw new BadRequestException('Package is not active or expired');
    }

    const validationResult = await this.validateOrHandleIncomingData(body, details);

    // Nếu validation không hợp lệ, trả về lỗi 400
    if (!validationResult.isValid) {
      const errorLog = this.memberApiLogRepo.create({
        configId,
        memberId: config.memberId,
        memberPackageId: packagePlanMember.id,
        host: memberAPI.host,
        url: memberAPI.path,
        request: body,
        response: { error: validationResult.message },
        isTest: body.isTest || false,
        method: memberAPI.method,
        statusCode: NSConfig.EApiStatusCode.BAD_REQUEST,
      });
      await this.memberApiLogRepo.save(errorLog);

      return {
        statusCode: validationResult.statusCode,
        message: validationResult.message,
        data: null,
      };
    }

    const isTest = body.isTest || false;
    delete body.isTest;

    const mapped = validationResult.data;
    const newLog = this.memberApiLogRepo.create({
      configId,
      memberId: config.memberId,
      memberPackageId: packagePlanMember.id, // Update lại dùng memberContextSession sau
      host: memberAPI.host,
      url: memberAPI.path,
      request: body,
      response: mapped,
      isTest,
      method: memberAPI.method,
      statusCode: NSConfig.EApiStatusCode.SUCCESS,
    });
    await this.memberApiLogRepo.save(newLog); // Ghi log handle call api

    if (isTest) {
      return {
        statusCode: 200,
        message: 'Test OK',
        data: mapped,
      };
    }

    if (packagePlanMember.currentTransaction >= packagePlanMember.initialTransactionLimit) {
      throw new BadRequestException('You have reached the maximum number of transactions');
    }

    await this.memberPackageRepo.update(
      {
        id: packagePlanMember.id,
      },
      {
        currentTransaction: packagePlanMember.currentTransaction + 1,
      },
    );

    // TODO: Call API to Chain

    return {
      statusCode: 200,
      message: 'OK',
      data: mapped,
    };
  }

  private async validateOrHandleIncomingData(body: any, details: ConfigPackageDetailEntity[]) {
    const result: Record<string, any> = {};

    for (const field of details) {
      const value = body[field.mappingField];

      // Nếu thiếu field bắt buộc
      if (field.isRequired && (value === undefined || value === null)) {
        return {
          statusCode: 400,
          isValid: false,
          message: `Missing required field: ${field.mappingField}`,
          data: null,
        };
      }

      switch (field.type.toLowerCase()) {
        case 'string':
          if (typeof value !== 'string') {
            return {
              statusCode: 400,
              isValid: false,
              message: `${field.mappingField} must be a string`,
              data: null,
            };
          }
          result[field.nameField] = value;
          break;

        case 'number':
          if (typeof value !== 'number') {
            return {
              statusCode: 400,
              isValid: false,
              message: `${field.mappingField} must be a number`,
              data: null,
            };
          }
          result[field.nameField] = value;
          break;

        case 'boolean':
          if (typeof value !== 'boolean') {
            return {
              statusCode: 400,
              isValid: false,
              message: `${field.mappingField} must be a boolean`,
              data: null,
            };
          }
          result[field.nameField] = value;
          break;

        case 'json':
          try {
            const parsed = typeof value === 'object' ? value : JSON.parse(value);
            result[field.nameField] = parsed;
          } catch (e) {
            return {
              statusCode: 400,
              isValid: false,
              message: `${field.mappingField} must be valid JSON`,
              data: null,
            };
          }
          break;

        case 'date':
          if (!dayjs(value).isValid()) {
            return {
              statusCode: 400,
              isValid: false,
              message: `${field.mappingField} must be a valid date`,
              data: null,
            };
          }
          result[field.nameField] = dayjs(value).toISOString();
          break;

        default:
          return {
            statusCode: 400,
            isValid: false,
            message: `Unsupported type: ${field.type}`,
            data: null,
          };
      }
    }

    return {
      statusCode: 200,
      isValid: true,
      message: 'Validation successful',
      data: result,
    };
  }
  //#endregion

  //#region Dashboard API Request Log
  async getLogStatsThisWeek(body: DashboardLogDto) {
    const { configId, isTest } = body;
    const { memberId } = memberSessionContext;
    const startOfWeek = dayjs().startOf('week').toDate();
    const endOfWeek = dayjs().endOf('week').toDate();

    const raw = await this.memberApiLogRepo
      .createQueryBuilder('log')
      .select(`EXTRACT(DOW FROM log."createdDate")::int`, 'day')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" = 200)`, 'success')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" != 200)`, 'fail')
      .where(`log."memberId" = :memberId`, { memberId })
      .andWhere(`log."configId" = :configId`, { configId })
      .andWhere(`log."isTest" = :isTest`, { isTest })
      .andWhere(`log."createdDate" BETWEEN :start AND :end`, {
        start: startOfWeek,
        end: endOfWeek,
      })
      .groupBy('day')
      .orderBy('day')
      .getRawMany();

    return this.formatChartData(raw, 7, 'week');
  }

  async getLogStatsThisMonth(body: DashboardLogDto) {
    const { memberId } = memberSessionContext;
    const { configId, isTest } = body;
    const startOfMonth = dayjs().startOf('month').toDate();
    const endOfMonth = dayjs().endOf('month').toDate();

    const raw = await this.memberApiLogRepo
      .createQueryBuilder('log')
      .select(`EXTRACT(DAY FROM log."createdDate")::int`, 'day')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" = 200)`, 'success')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" != 200)`, 'fail')
      .where(`log."memberId" = :memberId`, { memberId })
      .andWhere(`log."configId" = :configId`, { configId })
      .andWhere(`log."isTest" = :isTest`, { isTest })
      .andWhere(`log."createdDate" BETWEEN :start AND :end`, {
        start: startOfMonth,
        end: endOfMonth,
      })
      .groupBy('day')
      .orderBy('day')
      .getRawMany();

    const daysInMonth = dayjs().daysInMonth();
    return this.formatChartData(raw, daysInMonth, 'month');
  }

  async getLogStatsThisYear(body: DashboardLogDto) {
    const { memberId } = memberSessionContext;
    const { configId, isTest } = body;
    const startOfYear = dayjs().startOf('year').toDate();
    const endOfYear = dayjs().endOf('year').toDate();

    const raw = await this.memberApiLogRepo
      .createQueryBuilder('log')
      .select(`EXTRACT(MONTH FROM log."createdDate")::int`, 'month')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" = 200)`, 'success')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" != 200)`, 'fail')
      .where(`log."memberId" = :memberId`, { memberId })
      .andWhere(`log."configId" = :configId`, { configId })
      .andWhere(`log."isTest" = :isTest`, { isTest })
      .andWhere(`log."createdDate" BETWEEN :start AND :end`, {
        start: startOfYear,
        end: endOfYear,
      })
      .groupBy('month')
      .orderBy('month')
      .getRawMany();

    return this.formatChartData(raw, 12, 'year');
  }

  async getRequestStatsByConfig() {
    const { memberId } = memberSessionContext;
    const qb = this.memberApiLogRepo.createQueryBuilder('log')
      .select('log.configId', 'configId')
      .addSelect('COUNT(*)', 'count')
      .innerJoin('member_package', 'mp', 'mp.id = log."memberPackageId"')
      .innerJoin('package_plan', 'pkg', 'pkg.id = mp."packagePlanId"')
      .addSelect('pkg.name', 'packageName')
      .where('log.memberId = :memberId', { memberId });

    const raw = await qb
      .groupBy('log.configId')
      .addGroupBy('pkg.name')
      .orderBy('count', 'DESC')
      .getRawMany();

    return {
      labels: raw.map(r => r.packageName),
      datasets: [
        {
          label: 'Request count',
          data: raw.map(r => +r.count),
        },
      ],
    };
  }

  private formatChartData(
    raw: { day?: number; month?: number; success: string; fail: string }[],
    totalSlots: number,
    type: 'week' | 'month' | 'year'
  ) {
    const successData = new Array(totalSlots).fill(0);
    const failData = new Array(totalSlots).fill(0);

    raw.forEach(({ day, month, success, fail }) => {
      const index = (day ?? month) - (type === 'month' ? 1 : 0); // month: 1-12 => index 0-11, year: 1-12 => index 0-11
      successData[index] = +success;
      failData[index] = +fail;
    });

    return {
      totalRequest: raw.reduce((acc, cur) => acc + +cur.success + +cur.fail, 0),
      totalRequestSuccess: raw.reduce((acc, cur) => acc + +cur.success, 0),
      totalRequestFail: raw.reduce((acc, cur) => acc + +cur.fail, 0),
      labels: [...Array(totalSlots)].map((_, i) =>
        type === 'week'
          ? dateHelper.getDayOfWeek(i, 'en')
          : type === 'year'
          ? dateHelper.getMonthOfYear(i, 'en')
          : `${i + 1}`
      ),
      datasets: [
        {
          label: 'Success',
          data: successData,
          borderColor: '#6366f1',
          backgroundColor: 'rgba(70, 241, 57, 0.2)',
        },
        {
          label: 'Failed',
          data: failData,
          borderColor: '#f43f5e',
          backgroundColor: 'rgba(238, 24, 60, 0.2)',
        },
      ],
    };
  }
  //#endregion
}

