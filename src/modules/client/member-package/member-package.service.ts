import { Injectable, NotFoundException } from '@nestjs/common';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import {
  MemberPackageRepo,
  OrderRepo,
  MemberApiLogRepo,
  PackagePlanRepo,
  PaymentTransactionRepo,
  MemberRepo,
  ConfigPackageRepo,
} from '~/domains/primary';
import { MemberPackageListDto, MemberPackagePTDto } from './dto/member-package.dto';
import { Between, In } from 'typeorm';
import { memberSessionContext } from '../member-session.context';
import { NSMember } from '~/common/enums';
import Stripe from 'stripe';
import { configEnv } from '~/@config/env';

@Injectable()
export class MemberPackageService {
  private stripe: Stripe
  constructor(
  ) { this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {})}

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(OrderRepo)
  private orderRepo: OrderRepo;

  @BindRepo(PaymentTransactionRepo)
  private paymentTransactionRepo: PaymentTransactionRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  async findOne(id: string) {
    const packageMember = await this.memberPackageRepo.findOne({ where: { id } });
    if (!packageMember) throw new NotFoundException('Member package not found');

    const order = await this.orderRepo.findOne({ where: { id: packageMember.orderId } });
    if (!order) throw new NotFoundException('Order not found');

    const apiLogs = await this.memberApiLogRepo.find({
      where: { memberPackageId: packageMember.id },
    });

    return {
      package: packageMember,
      order,
      log: apiLogs,
    };
  }

  async findPagination(body: MemberPackageListDto) {
    const { memberId } = memberSessionContext;
    const { orderId, packagePlanId, status, expiredDateFrom, expiredDateTo, ...pageRequest } = body;
    const wheres: any = {};
    if (memberId) {
      wheres.memberId = memberId; // Update lại dùng memberSessionContext
    }
    if (orderId) {
      wheres.orderId = orderId;
    }
    if (packagePlanId) {
      wheres.packagePlanId = packagePlanId;
    }
    wheres.status = In([NSMember.EMemberPackageStatus.ACTIVE, NSMember.EMemberPackageStatus.EXPIRED]);
    if (status) {
      wheres.status = status;
    }
    if (expiredDateFrom && expiredDateTo) {
      wheres.expiredDate = Between(expiredDateFrom, expiredDateTo);
    }
    const { data, total } = await this.memberPackageRepo.findPagination(
      { where: wheres, order: { createdDate: 'DESC' } },
      pageRequest,
    );
    const plans = await this.packagePlanRepo.find();
    const mapping = data.map(item => {
      const plan = plans.find(p => p.id === item.packagePlanId);
      return {
        ...item,
        plan,
      };
    });
    return {
      data: mapping,
      total,
    };
  }

  async findAll() {
    const { memberId } = memberSessionContext;
    return this.memberPackageRepo.find({ where: { memberId, status: NSMember.EMemberPackageStatus.ACTIVE } });
  }

  // Danh sách giao dịch của một member-package
  async findTransaction(body: MemberPackagePTDto) {
    const { memberPackageId, ...pageRequest } = body;
    const packageMember = await this.memberPackageRepo.findOne({ where: { id: memberPackageId } });
    if (!packageMember) throw new NotFoundException('Member package not found');
    return this.paymentTransactionRepo.findPagination({ where: { memberPackageId: packageMember.id } }, pageRequest);
  }

  // Hủy tự động gia hạn gói bằng subscriptionId trên Stripe
  @DefTransaction()
  async cancelAutoRenewal(id: string) {
    const memberPackage = await this.memberPackageRepo.findOne({ where: { id } });
    if (!memberPackage) throw new NotFoundException('Member package not found');
    
    const { subscriptionId } = memberPackage;  
    if(subscriptionId) {
      await this.stripe.subscriptions.cancel(subscriptionId);
      return await this.memberPackageRepo.update({ id }, { isAutoRenewal: false }); 
    }
    return { message: 'No subscription found' };
  }
}
