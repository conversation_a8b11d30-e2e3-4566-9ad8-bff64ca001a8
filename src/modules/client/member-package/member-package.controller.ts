import { DefController, DefPost } from "~/@core/decorator";
import { MemberPackageService } from "./member-package.service";
import { MemberPackageListDto, MemberPackagePTDto } from "./dto/member-package.dto";
import { Body } from "@nestjs/common";

@DefController('member-package')
export class MemberPackageController {
    constructor(private readonly service: MemberPackageService) { }

    @DefPost('pagination', {
        summary: 'L<PERSON>y danh sách gói dịch vụ của member đã mua',
    })
    findPagination(@Body() body: MemberPackageListDto) {
        return this.service.findPagination(body);
    }

    @DefPost('find-all', {
        summary: '<PERSON><PERSON>y danh sách gói dịch vụ của member đã mua',
    })
    findAll() {
        return this.service.findAll();
    }

    @DefPost('detail', {
        summary: '<PERSON><PERSON><PERSON> thông tin chi tiết gói dịch vụ của member',
    })
    findOne(@Body("id") body: string) {
        return this.service.findOne(body);
    }

    @DefPost('transaction', {
        summary: '<PERSON><PERSON><PERSON> danh sách giao dịch của gói dịch vụ của member',
    })
    findTransaction(@Body() body: MemberPackagePTDto) {
        return this.service.findTransaction(body);
    }

    @DefPost('cancel-auto-renewal', {
        summary: 'Hủy tự động gia hạn gói dịch vụ',
    })
    cancelAutoRenewal(@Body("id") body: string) {
        return this.service.cancelAutoRenewal(body);
    }
}