import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { PageRequest } from "~/@systems/utils";

export class MemberPackageListDto extends PageRequest {
    @ApiProperty({ description: 'ID của đơn hàng' })
    @IsOptional()
    orderId?: string;

    @ApiProperty({ description: 'ID của gói dịch vụ' })
    @IsOptional()
    packagePlanId?: string;

    @ApiProperty({ description: 'Trạng thái' })
    @IsOptional()
    status?: string;

    @ApiProperty({ description: '<PERSON><PERSON><PERSON> hết hạn từ' })
    @IsOptional()
    expiredDateFrom?: Date;

    @ApiProperty({ description: 'Ngày hết hạn đến' })
    @IsOptional()
    expiredDateTo?: Date;
}

export class MemberPackagePTDto extends PageRequest {
    @ApiProperty({ description: 'ID của gói dịch vụ member đã mua' })
    @IsOptional()
    memberPackageId?: string;
}
    