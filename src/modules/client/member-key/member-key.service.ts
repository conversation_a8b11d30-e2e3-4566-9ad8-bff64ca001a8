import { Injectable } from "@nestjs/common";
import { BindRepo } from "~/@core/decorator";
import { MemberKeyRepo } from "~/domains/primary";
import { MemberRepo } from "~/domains/primary";
import { NSMember } from "~/common/enums";
import { MemberKeyDto } from "./dto";
import { memberSessionContext } from "../member-session.context";

@Injectable()
export class MemberKeyService {
    constructor() { }

    @BindRepo(MemberKeyRepo)
    private memberKeyRepo: MemberKeyRepo;

    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    // Khai báo key và cập nhật trạng thái member
    async generateKey(body: MemberKeyDto) {
        const { memberId } = memberSessionContext;
        const member = await this.memberRepo.findOne({ where: { id: memberId } });
        if (!member) throw new Error('Member not found');
        const { publicKey, apiKey } = body;

        await this.memberKeyRepo.save({
            memberId,
            publicKey,
            apiKey,
        });
        await this.memberRepo.update({ id: memberId }, { statusValidate: NSMember.EStatus.VALIDATED });
        return { message: "success" };
    }
}
