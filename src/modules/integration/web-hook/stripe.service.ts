import { HttpStatus, Injectable, NotFoundException, Req, Res } from "@nestjs/common";
import { Request, Response } from "express";
import { BindRepo } from "~/@core/decorator";
import { OrderRepo, PaymentTransactionRepo, PackagePlanRepo, MemberPackageRepo } from "~/domains/primary";
import Stripe from 'stripe'
import { configEnv } from "~/@config/env";
import { NSOrder } from "~/common/enums/order.enum";
import { NSPackage } from "~/common/enums/package.enum";
import { NSPayment } from "~/common/enums/payment.enum";
import * as dayjs from 'dayjs';
import { NSMember } from "~/common/enums";
import { StripeWebhookDto } from "./dto/stripe.dto";
import utc from 'dayjs/plugin/utc';
@Injectable()
export class StripeService {
    private stripe: Stripe
    constructor(
    ) {
        this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {
        })
    }

    @BindRepo(OrderRepo)
    orderRepo: OrderRepo;

    @BindRepo(PaymentTransactionRepo)
    paymentTransactionRepo: PaymentTransactionRepo;

    @BindRepo(MemberPackageRepo)
    private memberPackageRepo: MemberPackageRepo;

    @BindRepo(PackagePlanRepo)
    private packagePlanRepo: PackagePlanRepo;

    async handleWebhook(@Req() req: Request, @Res() res: Response) {
        const sig = req.headers['stripe-signature'];
        if (!sig) {
            return res.status(HttpStatus.BAD_REQUEST).send('No signature found');
        }

        const webhookSecret = configEnv().STRIPE_WEBHOOK_SECRET;
        let event: Stripe.Event;
        try {
            event = this.stripe.webhooks.constructEvent(
                req.body, // ⚠️ Raw body
                sig as string,
                webhookSecret
            );
        } catch (err) {
            console.error('Webhook signature verification failed.', err.message);
            return res.status(HttpStatus.BAD_REQUEST).send(`Webhook Error: ${err.message}`);
        }

        const session = event.data.object as Stripe.Checkout.Session;
        const metadata = session.metadata;
        const invoiceId = session.invoice as string;

        // Nếu không có metadata thì dừng lại
        if (metadata) {
            const { memberPackageId, orderId, transactionId, paymentType, timeRegister } = metadata;
            const subscriptionId = session.subscription as string;

            switch (event.type) {
                case 'checkout.session.completed':
                case 'checkout.session.async_payment_succeeded':
                    await this.updateOrderPaymentStatus({
                        orderId,
                        paymentTransactionId: transactionId,
                        paymentStatus: NSOrder.EPaymentStatus.PAID,
                        paymentType,
                        memberPackageId,
                        subscriptionId,
                        timeRegister,
                        invoiceId,
                        invoiceProvider: NSPayment.EPaymentProvider.STRIPE,
                    });
                    break;

                case 'checkout.session.async_payment_failed':
                    await this.updateOrderPaymentStatus({
                        orderId,
                        paymentTransactionId: transactionId,
                        paymentStatus: NSOrder.EPaymentStatus.UNPAID,
                        paymentType,
                        memberPackageId,
                        timeRegister,
                    });
                    break;
                default:
                    return res.status(HttpStatus.OK).send({ message: 'Ignored unhandled event' });
            }

            return res.status(HttpStatus.OK).send({
                message: 'Checkout session event processed successfully',
                data: metadata,
            });
        }
        return res.status(HttpStatus.OK).send({
            message: 'Checkout session event processed successfully',
        });
    }


    async updateOrderPaymentStatus(
        body: StripeWebhookDto,
    ) {
        const {
            orderId,
            paymentTransactionId,
            paymentStatus,
            paymentType,
            memberPackageId,
            timeRegister,
            subscriptionId,
            invoiceId,
            invoiceProvider,
        } = body;
  
        await this.orderRepo.update({
            id: orderId,
        }, {
            paymentStatus,
            status: paymentStatus === NSOrder.EPaymentStatus.PAID
                ? NSOrder.EStatus.COMPLETED
                : NSOrder.EStatus.PENDING,
            paymentDate: new Date().toISOString(),
            invoiceId,
            invoiceProvider,
        });
        await this.paymentTransactionRepo.update({
            id: paymentTransactionId,
        }, {
            status: paymentStatus === NSOrder.EPaymentStatus.PAID
                ? NSPayment.ETransactionStatus.COMPLETED
                : NSPayment.ETransactionStatus.FAILED,
            transactionDate: new Date().toISOString(),
        });

        if (paymentStatus === NSOrder.EPaymentStatus.PAID) {
            const pkg = await this.memberPackageRepo.findOne({ where: { id: memberPackageId } });
            if (!pkg) throw new NotFoundException('Member package not found');

            const plan = await this.packagePlanRepo.findOne({ where: { id: pkg.packagePlanId } });
            if (!plan) throw new NotFoundException('Plan not found');

            const unit = paymentType === NSPackage.EPlanTypePayment.MONTHLY ? 'month' : 'year';
            const expiredDate = dayjs().add(+timeRegister, unit).format('YYYY-MM-DD');

            await this.memberPackageRepo.update({
                id: memberPackageId,
            }, {
                status: NSMember.EMemberPackageStatus.ACTIVE,
                expiredDate,
                activatedDate: new Date().toISOString(),
            });
        }
    }

    // Xử lý tự động gia hạn, tạo giao dịch cho memberPackage khi stripe tự động thanh toán
    async handleAutoRenewalFromInvoice(event: Stripe.Event) {
        const invoice: any = event.data.object as Stripe.Invoice;
        const paymentIntentId = invoice.payment_intent as string;

        const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

        const {
            memberId,
            orderId,
            memberPackageId,
            paymentType
        } = invoice.metadata;

        // Kiểm tra tránh tạo trùng giao dịch
        const existing = await this.paymentTransactionRepo.findOne({
            where: { refId: paymentIntent.id }
        });
        if (existing) return;

        await this.paymentTransactionRepo.save({
            memberId,
            amount: paymentIntent.amount_received,
            grossAmount: paymentIntent.amount_received,
            paymentProvider: NSPayment.EPaymentProvider.STRIPE,
            paymentMethod: NSPayment.EPaymentMethod.PAY_CARD,
            refId: paymentIntent.id,
            clientSecret: paymentIntent.client_secret,
            status: NSPayment.ETransactionStatus.COMPLETED,
            transactionType: NSPayment.ETransactionType.RENEWAL,
            transactionDate: new Date().toISOString(),
            memberPackageId,
        });

        const unit = paymentType === NSPackage.EPlanTypePayment.MONTHLY ? 'month' : 'year';
        await this.memberPackageRepo.update({
            id: memberPackageId,
        }, {
            expiredDate: dayjs().add(1, unit).format('YYYY-MM-DD'),
        });
    }
}