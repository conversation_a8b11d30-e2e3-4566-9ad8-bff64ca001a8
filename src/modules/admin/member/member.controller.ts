import { Body, Param, Query } from '@nestjs/common';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { MemberService } from './member.service';
import { MemberListDto } from './dto/member.dto';
import { PageRequest } from '~/@systems/utils';

@DefController('member')
export class MemberController {
  constructor(private readonly memberService: MemberService) {}

  @DefGet('', {
    summary: 'Lấy danh sách thành viên',
  })
  findAll(@Query() queries: MemberListDto) {
    return this.memberService.findAll(queries);
  }

  @DefPost('packages', {
    summary: 'Lấy danh sách gói dịch vụ của thành viên',
  })
  getPackages(@Body() body: any) {
    return this.memberService.getPackagesDetail(body);
  }

  @DefPost('orders', {
    summary: '<PERSON><PERSON>y danh sách đơn hàng của thành viên',
  })
  getOrders(@Body() body: any) {
    return this.memberService.getOrdersDetail(body);
  }

  @DefPost('config-packages', {
    summary: 'Lấy danh sách cấu hình của thành viên',
  })
  getConfigPackages(@Body() body: any) {
    return this.memberService.getConfigPackages(body);
  }
  
  // Block/Unblock Member
  @DefPost('block', {
    summary: 'Block member',
  })
  block(@Body() body: any) {
    return this.memberService.block(body);
  }

  @DefPost('unblock', {
    summary: 'Unblock member',
  })
  unblock(@Body() body: any) {
    return this.memberService.unblock(body);
  }

}
